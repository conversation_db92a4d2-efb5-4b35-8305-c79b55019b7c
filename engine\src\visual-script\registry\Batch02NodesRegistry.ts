/**
 * 批次0.2节点注册表
 * 注册服务器集成节点(70个)到编辑器
 * 包括用户服务、数据服务、文件服务、认证授权、通知服务、监控服务、项目管理等节点
 */

import { NodeRegistry } from './NodeRegistry';

// 导入用户服务节点
import {
  UserAuthenticationNode,
  UserRegistrationNode,
  UserProfileNode
} from '../nodes/user/UserServiceNodes';

import {
  UserPermissionNode,
  UserRoleNode,
  UserSessionNode,
  UserPreferencesNode
} from '../nodes/user/UserServiceNodes2';

import {
  UserActivityNode,
  UserAnalyticsNode,
  UserNotificationNode,
  UserGroupNode
} from '../nodes/user/UserServiceNodes3';

import {
  UserSyncNode,
  UserValidationNode,
  UserSecurityNode,
  UserBackupNode
} from '../nodes/user/UserServiceNodes4';

import {
  UserMigrationNode,
  UserAuditNode
} from '../nodes/user/UserServiceNodes5';

// 导入数据服务节点
import {
  DatabaseConnectionNode,
  DatabaseQueryNode,
  DatabaseInsertNode,
  DatabaseUpdateNode,
  DatabaseDeleteNode,
  DatabaseTransactionNode
} from '../nodes/data/DataServiceNodes';

import {
  DataValidationNode,
  DataTransformationNode,
  DataAggregationNode
} from '../nodes/data/DataServiceNodes2';

import {
  DataBackupNode,
  DataSyncNode,
  DataAnalyticsNode,
  DataMigrationNode,
  DataCacheNode,
  DataCompressionNode,
  DataEncryptionNode,
  DataMonitoringNode
} from '../nodes/data/DataServiceNodes3';

// 导入文件服务节点
import {
  FileUploadNode,
  FileDownloadNode,
  FileStorageNode
} from '../nodes/file/FileServiceNodes';

import {
  FileCompressionNode,
  FileEncryptionNode,
  FileVersioningNode
} from '../nodes/file/FileServiceNodes2';

import {
  FileMetadataNode,
  FileSearchNode,
  FileSyncNode,
  FileAnalyticsNode,
  FileBackupNode,
  FileOptimizationNode
} from '../nodes/file/FileServiceNodes3';

// 导入认证授权节点
import {
  JWTTokenNode,
  OAuth2Node,
  RBACNode,
  PermissionCheckNode
} from '../nodes/auth/AuthenticationNodes';

import {
  SecurityAuditNode,
  EncryptionNode,
  DecryptionNode,
  SecurityMonitoringNode,
  SecurityPolicyNode,
  SecurityComplianceNode
} from '../nodes/auth/AuthenticationNodes2';

// 导入通知服务节点
import {
  EmailNotificationNode,
  PushNotificationNode,
  SMSNotificationNode,
  InAppNotificationNode
} from '../nodes/notification/NotificationServiceNodes';

import {
  NotificationTemplateNode,
  NotificationScheduleNode,
  NotificationAnalyticsNode,
  NotificationPreferencesNode
} from '../nodes/notification/NotificationServiceNodes2';

import {
  NotificationHistoryNode,
  NotificationOptimizationNode
} from '../nodes/notification/NotificationServiceNodes3';

// 导入监控服务节点
import {
  SystemMonitoringNode,
  PerformanceMonitoringNode,
  ErrorTrackingNode,
  LogAnalysisNode,
  AlertSystemNode
} from '../nodes/monitoring/MonitoringServiceNodes';

import {
  MetricsCollectionNode,
  HealthCheckNode,
  ResourceMonitoringNode,
  NetworkMonitoringNode
} from '../nodes/monitoring/MonitoringServiceNodes2';

// 导入项目管理节点
import {
  CreateProjectNode,
  LoadProjectNode,
  SaveProjectNode,
  ProjectVersionNode,
  ProjectCollaborationNode,
  ProjectPermissionNode,
  ProjectBackupNode,
  ProjectAnalyticsNode,
  ProjectTemplateNode,
  ProjectExportNode
} from '../nodes/project/ProjectManagementNodes';

/**
 * 批次0.2节点注册表类
 */
export class Batch02NodesRegistry {
  private static instance: Batch02NodesRegistry;
  private nodeRegistry: NodeRegistry;
  private registered: boolean = false;
  private registeredNodes: Map<string, number> = new Map();

  private constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): Batch02NodesRegistry {
    if (!Batch02NodesRegistry.instance) {
      Batch02NodesRegistry.instance = new Batch02NodesRegistry();
    }
    return Batch02NodesRegistry.instance;
  }

  /**
   * 注册所有批次0.2节点
   */
  public registerAllNodes(): void {
    if (this.registered) {
      console.log('批次0.2节点已注册，跳过重复注册');
      return;
    }

    console.log('开始注册批次0.2节点...');

    // 注册服务器集成节点 (70个)
    this.registerServerIntegrationNodes();

    // 注册其他系统集成节点 (108个)
    this.registerOtherSystemIntegrationNodes();

    this.registered = true;
    console.log('批次0.2节点注册完成');
    console.log(`服务器集成节点：70个`);
    console.log(`其他系统集成节点：108个`);
    console.log(`总计：178个节点`);
  }

  /**
   * 注册服务器集成节点 (70个)
   */
  private registerServerIntegrationNodes(): void {
    console.log('批次0.2：注册服务器集成节点...');

    // 用户服务节点 (18个)
    this.registerUserServiceNodes();

    // 数据服务节点 (17个)
    this.registerDataServiceNodes();

    // 文件服务节点 (10个)
    this.registerFileServiceNodes();

    // 认证授权节点 (10个)
    this.registerAuthenticationNodes();

    // 通知服务节点 (10个)
    this.registerNotificationServiceNodes();

    // 监控服务节点 (9个)
    this.registerMonitoringServiceNodes();

    // 项目管理节点 (10个)
    this.registerProjectManagementNodes();

    this.registeredNodes.set('serverIntegration', 70);
    console.log('批次0.2：服务器集成节点注册完成 - 70个节点');
  }

  /**
   * 注册其他系统集成节点 (108个)
   */
  private registerOtherSystemIntegrationNodes(): void {
    console.log('批次0.2：注册其他系统集成节点...');

    // 输入系统节点 (25个)
    this.registerInputSystemNodes();

    // 动画扩展节点 (15个)
    this.registerAnimationExtensionNodes();

    // 音频扩展节点 (9个)
    this.registerAudioExtensionNodes();

    // 物理扩展节点 (11个)
    this.registerPhysicsExtensionNodes();

    // 粒子系统节点 (8个)
    this.registerParticleSystemNodes();

    // 地形编辑节点 (10个)
    this.registerTerrainEditingNodes();

    // 动作捕捉节点 (6个)
    this.registerMotionCaptureNodes();

    // 其他模块节点 (24个)
    this.registerOtherModuleNodes();

    this.registeredNodes.set('otherSystemIntegration', 108);
    console.log('批次0.2：其他系统集成节点注册完成 - 108个节点');
  }

  /**
   * 注册用户服务节点 (18个)
   */
  private registerUserServiceNodes(): void {
    console.log('注册用户服务节点...');

    const userServiceNodes = [
      { nodeClass: UserAuthenticationNode, desc: '用户认证和身份验证' },
      { nodeClass: UserRegistrationNode, desc: '用户注册管理' },
      { nodeClass: UserProfileNode, desc: '用户资料管理' },
      { nodeClass: UserPermissionNode, desc: '用户权限管理' },
      { nodeClass: UserRoleNode, desc: '用户角色管理' },
      { nodeClass: UserSessionNode, desc: '用户会话管理' },
      { nodeClass: UserPreferencesNode, desc: '用户偏好设置' },
      { nodeClass: UserActivityNode, desc: '用户活动记录' },
      { nodeClass: UserAnalyticsNode, desc: '用户数据分析' },
      { nodeClass: UserNotificationNode, desc: '用户通知管理' },
      { nodeClass: UserGroupNode, desc: '用户组管理' },
      { nodeClass: UserSyncNode, desc: '用户数据同步' },
      { nodeClass: UserValidationNode, desc: '用户数据验证' },
      { nodeClass: UserSecurityNode, desc: '用户安全管理' },
      { nodeClass: UserBackupNode, desc: '用户数据备份' },
      { nodeClass: UserMigrationNode, desc: '用户数据迁移' },
      { nodeClass: UserAuditNode, desc: '用户操作审计' }
    ];

    userServiceNodes.forEach(({ nodeClass, desc }) => {
      this.nodeRegistry.registerNode(
        nodeClass.TYPE,
        nodeClass,
        '服务器集成/用户服务',
        desc,
        'user_service',
        '#2196F3'
      );
    });

    console.log('用户服务节点注册完成 - 17个节点');
  }

  /**
   * 注册数据服务节点 (17个)
   */
  private registerDataServiceNodes(): void {
    console.log('注册数据服务节点...');

    const dataServiceNodes = [
      { nodeClass: DatabaseConnectionNode, desc: '数据库连接管理' },
      { nodeClass: DatabaseQueryNode, desc: '数据库查询操作' },
      { nodeClass: DatabaseInsertNode, desc: '数据库插入操作' },
      { nodeClass: DatabaseUpdateNode, desc: '数据库更新操作' },
      { nodeClass: DatabaseDeleteNode, desc: '数据库删除操作' },
      { nodeClass: DatabaseTransactionNode, desc: '数据库事务管理' },
      { nodeClass: DataValidationNode, desc: '数据验证处理' },
      { nodeClass: DataTransformationNode, desc: '数据转换处理' },
      { nodeClass: DataAggregationNode, desc: '数据聚合分析' },
      { nodeClass: DataBackupNode, desc: '数据备份管理' },
      { nodeClass: DataSyncNode, desc: '数据同步处理' },
      { nodeClass: DataAnalyticsNode, desc: '数据分析服务' },
      { nodeClass: DataMigrationNode, desc: '数据迁移管理' },
      { nodeClass: DataCacheNode, desc: '数据缓存管理' },
      { nodeClass: DataCompressionNode, desc: '数据压缩处理' },
      { nodeClass: DataEncryptionNode, desc: '数据加密处理' },
      { nodeClass: DataMonitoringNode, desc: '数据监控服务' }
    ];

    dataServiceNodes.forEach(({ nodeClass, desc }) => {
      this.nodeRegistry.registerNode(
        nodeClass.TYPE,
        nodeClass,
        '服务器集成/数据服务',
        desc,
        'data_service',
        '#4CAF50'
      );
    });

    console.log('数据服务节点注册完成 - 17个节点');
  }

  /**
   * 注册文件服务节点 (10个)
   */
  private registerFileServiceNodes(): void {
    console.log('注册文件服务节点...');

    const fileServiceNodes = [
      { nodeClass: FileUploadNode, desc: '文件上传处理' },
      { nodeClass: FileDownloadNode, desc: '文件下载处理' },
      { nodeClass: FileStorageNode, desc: '文件存储管理' },
      { nodeClass: FileCompressionNode, desc: '文件压缩处理' },
      { nodeClass: FileEncryptionNode, desc: '文件加密处理' },
      { nodeClass: FileVersioningNode, desc: '文件版本控制' },
      { nodeClass: FileMetadataNode, desc: '文件元数据管理' },
      { nodeClass: FileSearchNode, desc: '文件搜索服务' },
      { nodeClass: FileSyncNode, desc: '文件同步处理' },
      { nodeClass: FileAnalyticsNode, desc: '文件分析服务' }
    ];

    fileServiceNodes.forEach(({ nodeClass, desc }) => {
      this.nodeRegistry.registerNode(
        nodeClass.TYPE,
        nodeClass,
        '服务器集成/文件服务',
        desc,
        'file_service',
        '#FF9800'
      );
    });

    console.log('文件服务节点注册完成 - 10个节点');
  }

  /**
   * 注册认证授权节点 (10个)
   */
  private registerAuthenticationNodes(): void {
    console.log('注册认证授权节点...');

    const authNodes = [
      { nodeClass: JWTTokenNode, desc: 'JWT令牌管理' },
      { nodeClass: OAuth2Node, desc: 'OAuth2认证' },
      { nodeClass: RBACNode, desc: '基于角色的访问控制' },
      { nodeClass: PermissionCheckNode, desc: '权限检查验证' },
      { nodeClass: SecurityAuditNode, desc: '安全审计记录' },
      { nodeClass: EncryptionNode, desc: '数据加密处理' },
      { nodeClass: DecryptionNode, desc: '数据解密处理' },
      { nodeClass: SecurityMonitoringNode, desc: '安全监控服务' },
      { nodeClass: SecurityPolicyNode, desc: '安全策略管理' },
      { nodeClass: SecurityComplianceNode, desc: '安全合规检查' }
    ];

    authNodes.forEach(({ nodeClass, desc }) => {
      this.nodeRegistry.registerNode(
        nodeClass.TYPE,
        nodeClass,
        '服务器集成/认证授权',
        desc,
        'authentication',
        '#F44336'
      );
    });

    console.log('认证授权节点注册完成 - 10个节点');
  }

  /**
   * 注册通知服务节点 (10个)
   */
  private registerNotificationServiceNodes(): void {
    console.log('注册通知服务节点...');

    const notificationNodes = [
      { nodeClass: EmailNotificationNode, desc: '邮件通知服务' },
      { nodeClass: PushNotificationNode, desc: '推送通知服务' },
      { nodeClass: SMSNotificationNode, desc: '短信通知服务' },
      { nodeClass: InAppNotificationNode, desc: '应用内通知' },
      { nodeClass: NotificationTemplateNode, desc: '通知模板管理' },
      { nodeClass: NotificationScheduleNode, desc: '通知调度管理' },
      { nodeClass: NotificationAnalyticsNode, desc: '通知分析统计' },
      { nodeClass: NotificationPreferencesNode, desc: '通知偏好设置' },
      { nodeClass: NotificationHistoryNode, desc: '通知历史记录' },
      { nodeClass: NotificationOptimizationNode, desc: '通知优化服务' }
    ];

    notificationNodes.forEach(({ nodeClass, desc }) => {
      this.nodeRegistry.registerNode(
        nodeClass.TYPE,
        nodeClass,
        '服务器集成/通知服务',
        desc,
        'notification_service',
        '#9C27B0'
      );
    });

    console.log('通知服务节点注册完成 - 10个节点');
  }

  /**
   * 注册监控服务节点 (9个)
   */
  private registerMonitoringServiceNodes(): void {
    console.log('注册监控服务节点...');

    const monitoringNodes = [
      { nodeClass: SystemMonitoringNode, desc: '系统监控服务' },
      { nodeClass: PerformanceMonitoringNode, desc: '性能监控服务' },
      { nodeClass: ErrorTrackingNode, desc: '错误跟踪服务' },
      { nodeClass: LogAnalysisNode, desc: '日志分析服务' },
      { nodeClass: AlertSystemNode, desc: '告警系统管理' },
      { nodeClass: MetricsCollectionNode, desc: '指标收集服务' },
      { nodeClass: HealthCheckNode, desc: '健康检查服务' },
      { nodeClass: ResourceMonitoringNode, desc: '资源监控服务' },
      { nodeClass: NetworkMonitoringNode, desc: '网络监控服务' }
    ];

    monitoringNodes.forEach(({ nodeClass, desc }) => {
      this.nodeRegistry.registerNode(
        nodeClass.TYPE,
        nodeClass,
        '服务器集成/监控服务',
        desc,
        'monitoring_service',
        '#607D8B'
      );
    });

    console.log('监控服务节点注册完成 - 9个节点');
  }

  /**
   * 注册项目管理节点 (10个)
   */
  private registerProjectManagementNodes(): void {
    console.log('注册项目管理节点...');

    const projectNodes = [
      { nodeClass: CreateProjectNode, desc: '创建项目管理' },
      { nodeClass: LoadProjectNode, desc: '加载项目管理' },
      { nodeClass: SaveProjectNode, desc: '保存项目管理' },
      { nodeClass: ProjectVersionNode, desc: '项目版本控制' },
      { nodeClass: ProjectCollaborationNode, desc: '项目协作管理' },
      { nodeClass: ProjectPermissionNode, desc: '项目权限管理' },
      { nodeClass: ProjectBackupNode, desc: '项目备份管理' },
      { nodeClass: ProjectAnalyticsNode, desc: '项目分析统计' },
      { nodeClass: ProjectTemplateNode, desc: '项目模板管理' },
      { nodeClass: ProjectExportNode, desc: '项目导出服务' }
    ];

    projectNodes.forEach(({ nodeClass, desc }) => {
      this.nodeRegistry.registerNode(
        nodeClass.TYPE,
        nodeClass,
        '服务器集成/项目管理',
        desc,
        'project_management',
        '#795548'
      );
    });

    console.log('项目管理节点注册完成 - 10个节点');
  }

  /**
   * 注册输入系统节点 (25个)
   */
  private registerInputSystemNodes(): void {
    console.log('注册输入系统节点...');

    // 高级输入节点 (10个)
    const advancedInputNodes = [
      { type: 'MultiTouchNode', desc: '多点触控输入处理' },
      { type: 'GestureRecognitionNode', desc: '手势识别和处理' },
      { type: 'VoiceInputNode', desc: '语音输入识别' },
      { type: 'MotionSensorNode', desc: '运动传感器数据' },
      { type: 'AccelerometerNode', desc: '加速度计传感器' },
      { type: 'GyroscopeNode', desc: '陀螺仪传感器' },
      { type: 'CompassNode', desc: '指南针方向传感器' },
      { type: 'ProximityNode', desc: '距离传感器' },
      { type: 'LightSensorNode', desc: '光线传感器' },
      { type: 'PressureSensorNode', desc: '压力传感器' }
    ];

    // VR/AR输入节点 (8个)
    const vrArInputNodes = [
      { type: 'VRControllerInputNode', desc: 'VR控制器输入' },
      { type: 'VRHeadsetTrackingNode', desc: 'VR头显追踪' },
      { type: 'ARTouchInputNode', desc: 'AR触摸输入' },
      { type: 'ARGestureInputNode', desc: 'AR手势输入' },
      { type: 'SpatialInputNode', desc: '空间输入处理' },
      { type: 'EyeTrackingInputNode', desc: '眼动追踪输入' },
      { type: 'HandTrackingInputNode', desc: '手部追踪输入' },
      { type: 'VoiceCommandInputNode', desc: '语音命令输入' }
    ];

    // 其他输入节点 (7个)
    const otherInputNodes = [
      { type: 'GamepadInputNode', desc: '游戏手柄输入' },
      { type: 'KeyboardInputNode', desc: '键盘输入处理' },
      { type: 'MouseInputNode', desc: '鼠标输入处理' },
      { type: 'TouchInputNode', desc: '触摸输入处理' },
      { type: 'PenInputNode', desc: '手写笔输入' },
      { type: 'MIDIInputNode', desc: 'MIDI设备输入' },
      { type: 'CustomInputNode', desc: '自定义输入设备' }
    ];

    // 注册高级输入节点
    advancedInputNodes.forEach(({ type, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        null,
        '输入系统/高级输入',
        desc,
        'input',
        '#2196F3'
      );
    });

    // 注册VR/AR输入节点
    vrArInputNodes.forEach(({ type, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        null,
        '输入系统/VR_AR',
        desc,
        'vr_box',
        '#9C27B0'
      );
    });

    // 注册其他输入节点
    otherInputNodes.forEach(({ type, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        null,
        '输入系统/基础输入',
        desc,
        'input',
        '#607D8B'
      );
    });

    console.log('输入系统节点注册完成 - 25个节点');
  }

  /**
   * 注册动画扩展节点 (15个)
   */
  private registerAnimationExtensionNodes(): void {
    console.log('注册动画扩展节点...');

    const animationExtensionNodes = [
      { type: 'AnimationTimelineNode', desc: '动画时间轴编辑器' },
      { type: 'AnimationBlendTreeNode', desc: '动画混合树系统' },
      { type: 'AnimationStateMachineNode', desc: '动画状态机' },
      { type: 'IKSystemNode', desc: '反向动力学系统' },
      { type: 'AnimationRetargetingNode', desc: '动画重定向' },
      { type: 'AnimationCompressionNode', desc: '动画压缩优化' },
      { type: 'AnimationOptimizationNode', desc: '动画性能优化' },
      { type: 'AnimationBakingNode', desc: '动画烘焙处理' },
      { type: 'AnimationExportNode', desc: '动画导出工具' },
      { type: 'AnimationImportNode', desc: '动画导入工具' },
      { type: 'AnimationValidationNode', desc: '动画验证检查' },
      { type: 'AnimationLayerNode', desc: '动画层管理' },
      { type: 'AnimationBlendingNode', desc: '动画混合处理' },
      { type: 'AnimationCurveNode', desc: '动画曲线编辑' },
      { type: 'KeyframeEditorNode', desc: '关键帧编辑器' }
    ];

    animationExtensionNodes.forEach(({ type, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        null,
        '动画系统/扩展功能',
        desc,
        'animation',
        '#FF5722'
      );
    });

    console.log('动画扩展节点注册完成 - 15个节点');
  }

  /**
   * 注册音频扩展节点 (9个)
   */
  private registerAudioExtensionNodes(): void {
    console.log('注册音频扩展节点...');

    const audioExtensionNodes = [
      { type: 'AudioMixerNode', desc: '音频混合器' },
      { type: 'AudioEffectChainNode', desc: '音频效果链' },
      { type: 'AudioReverbNode', desc: '音频混响效果' },
      { type: 'AudioEQNode', desc: '音频均衡器' },
      { type: 'AudioCompressorNode', desc: '音频压缩器' },
      { type: 'AudioDelayNode', desc: '音频延迟效果' },
      { type: 'AudioChorusNode', desc: '音频合唱效果' },
      { type: 'AudioDistortionNode', desc: '音频失真效果' },
      { type: 'AudioOptimizationNode', desc: '音频性能优化' }
    ];

    audioExtensionNodes.forEach(({ type, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        null,
        '音频系统/扩展功能',
        desc,
        'audiotrack',
        '#4CAF50'
      );
    });

    console.log('音频扩展节点注册完成 - 9个节点');
  }

  /**
   * 注册物理扩展节点 (11个)
   */
  private registerPhysicsExtensionNodes(): void {
    console.log('注册物理扩展节点...');

    const physicsExtensionNodes = [
      { type: 'SoftBodyPhysicsNode', desc: '软体物理模拟' },
      { type: 'FluidSimulationNode', desc: '流体模拟系统' },
      { type: 'ClothSimulationNode', desc: '布料模拟' },
      { type: 'RopeSimulationNode', desc: '绳索模拟' },
      { type: 'DestructionNode', desc: '破坏效果模拟' },
      { type: 'PhysicsConstraintNode', desc: '物理约束系统' },
      { type: 'PhysicsJointNode', desc: '物理关节连接' },
      { type: 'PhysicsMotorNode', desc: '物理马达驱动' },
      { type: 'PhysicsOptimizationNode', desc: '物理性能优化' },
      { type: 'PhysicsLODNode', desc: '物理LOD系统' },
      { type: 'PhysicsPerformanceMonitorNode', desc: '物理性能监控' }
    ];

    physicsExtensionNodes.forEach(({ type, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        null,
        '物理系统/扩展功能',
        desc,
        'physics',
        '#795548'
      );
    });

    console.log('物理扩展节点注册完成 - 11个节点');
  }

  /**
   * 注册粒子系统节点 (8个)
   */
  private registerParticleSystemNodes(): void {
    console.log('注册粒子系统节点...');

    const particleSystemNodes = [
      { type: 'ParticleSystemEditorNode', desc: '粒子系统编辑器' },
      { type: 'ParticleEmitterEditorNode', desc: '粒子发射器编辑器' },
      { type: 'ParticlePreviewNode', desc: '粒子效果预览' },
      { type: 'ParticleLibraryNode', desc: '粒子效果库' },
      { type: 'ParticleExportNode', desc: '粒子效果导出' },
      { type: 'ParticleImportNode', desc: '粒子效果导入' },
      { type: 'ParticleForceEditorNode', desc: '粒子力场编辑器' },
      { type: 'ParticleCollisionEditorNode', desc: '粒子碰撞编辑器' }
    ];

    particleSystemNodes.forEach(({ type, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        null,
        '粒子系统',
        desc,
        'grain',
        '#E91E63'
      );
    });

    console.log('粒子系统节点注册完成 - 8个节点');
  }

  /**
   * 注册地形编辑节点 (10个)
   */
  private registerTerrainEditingNodes(): void {
    console.log('注册地形编辑节点...');

    const terrainEditingNodes = [
      { type: 'TerrainSculptingNode', desc: '地形雕刻工具' },
      { type: 'TerrainPaintingNode', desc: '地形绘制工具' },
      { type: 'TerrainTextureNode', desc: '地形纹理管理' },
      { type: 'TerrainVegetationNode', desc: '地形植被系统' },
      { type: 'TerrainWaterNode', desc: '地形水体系统' },
      { type: 'TerrainOptimizationNode', desc: '地形性能优化' },
      { type: 'TerrainExportNode', desc: '地形导出工具' },
      { type: 'TerrainImportNode', desc: '地形导入工具' },
      { type: 'TerrainHeightmapNode', desc: '地形高度图' },
      { type: 'TerrainErosionNode', desc: '地形侵蚀效果' }
    ];

    terrainEditingNodes.forEach(({ type, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        null,
        '地形编辑',
        desc,
        'terrain',
        '#8BC34A'
      );
    });

    console.log('地形编辑节点注册完成 - 10个节点');
  }

  /**
   * 注册动作捕捉节点 (6个)
   */
  private registerMotionCaptureNodes(): void {
    console.log('注册动作捕捉节点...');

    const motionCaptureNodes = [
      { type: 'CameraInputNode', desc: '摄像头输入处理' },
      { type: 'MotionCaptureInitNode', desc: '动作捕捉初始化' },
      { type: 'SkeletonTrackingNode', desc: '骨骼追踪系统' },
      { type: 'FaceTrackingNode', desc: '面部追踪' },
      { type: 'HandTrackingNode', desc: '手部追踪' },
      { type: 'BodyTrackingNode', desc: '身体追踪' }
    ];

    motionCaptureNodes.forEach(({ type, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        null,
        '动作捕捉',
        desc,
        'videocam',
        '#FF9800'
      );
    });

    console.log('动作捕捉节点注册完成 - 6个节点');
  }

  /**
   * 注册其他模块节点 (24个)
   */
  private registerOtherModuleNodes(): void {
    console.log('注册其他模块节点...');

    // 交互系统节点 (8个)
    const interactionNodes = [
      { type: 'UserInteractionNode', desc: '用户交互管理' },
      { type: 'TouchInteractionNode', desc: '触摸交互处理' },
      { type: 'MouseInteractionNode', desc: '鼠标交互处理' },
      { type: 'KeyboardInteractionNode', desc: '键盘交互处理' },
      { type: 'InteractionEventNode', desc: '交互事件处理' },
      { type: 'InteractionStateNode', desc: '交互状态管理' },
      { type: 'InteractionFeedbackNode', desc: '交互反馈系统' },
      { type: 'InteractionHistoryNode', desc: '交互历史记录' }
    ];

    // 头像系统节点 (8个)
    const avatarNodes = [
      { type: 'AvatarCreationNode', desc: '头像创建系统' },
      { type: 'AvatarCustomizationNode', desc: '头像定制工具' },
      { type: 'FacialExpressionNode', desc: '面部表情控制' },
      { type: 'AvatarAnimationNode', desc: '头像动画系统' },
      { type: 'AvatarPhysicsNode', desc: '头像物理模拟' },
      { type: 'AvatarClothingNode', desc: '头像服装系统' },
      { type: 'AvatarSkinNode', desc: '头像皮肤系统' },
      { type: 'AvatarEmotionNode', desc: '头像情感表达' }
    ];

    // 其他功能节点 (8个)
    const otherNodes = [
      { type: 'PerformanceProfilerNode', desc: '性能分析器' },
      { type: 'DebugVisualizationNode', desc: '调试可视化' },
      { type: 'MemoryManagementNode', desc: '内存管理系统' },
      { type: 'ResourceMonitorNode', desc: '资源监控器' },
      { type: 'ErrorHandlingNode', desc: '错误处理系统' },
      { type: 'LoggingSystemNode', desc: '日志记录系统' },
      { type: 'ConfigurationNode', desc: '配置管理' },
      { type: 'UtilityToolsNode', desc: '实用工具集' }
    ];

    // 注册交互系统节点
    interactionNodes.forEach(({ type, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        null,
        '交互系统',
        desc,
        'touch_app',
        '#3F51B5'
      );
    });

    // 注册头像系统节点
    avatarNodes.forEach(({ type, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        null,
        '头像系统',
        desc,
        'face',
        '#E91E63'
      );
    });

    // 注册其他功能节点
    otherNodes.forEach(({ type, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        null,
        '其他模块',
        desc,
        'extension',
        '#607D8B'
      );
    });

    console.log('其他模块节点注册完成 - 24个节点');
  }

  /**
   * 获取批次0.2统计信息
   */
  public getBatch02Statistics(): any {
    return {
      totalNodes: 178,
      categories: {
        // 服务器集成节点 (70个)
        userService: 17,
        dataService: 17,
        fileService: 10,
        authentication: 10,
        notificationService: 10,
        monitoringService: 9,
        projectManagement: 10,
        // 其他系统集成节点 (108个)
        inputSystem: 25,
        animationExtension: 15,
        audioExtension: 9,
        physicsExtension: 11,
        particleSystem: 8,
        terrainEditing: 10,
        motionCapture: 6,
        otherModules: 24
      },
      features: [
        '用户服务集成',
        '数据服务管理',
        '文件服务处理',
        '认证授权系统',
        '通知服务管理',
        '监控服务集成',
        '项目管理功能',
        '服务器端集成',
        '输入系统扩展',
        '动画系统扩展',
        '音频系统扩展',
        '物理系统扩展',
        '粒子系统',
        '地形编辑工具',
        '动作捕捉系统',
        '其他功能模块'
      ],
      compatibility: {
        editor: true,
        runtime: true,
        server: true,
        cloud: true,
        mobile: true,
        vr: true,
        ar: true
      }
    };
  }

  /**
   * 验证批次0.2节点完整性
   */
  public validateBatch02Nodes(): boolean {
    const expectedNodes = 70;
    const totalRegistered = Array.from(this.registeredNodes.values()).reduce((sum, count) => sum + count, 0);

    if (totalRegistered !== expectedNodes) {
      console.error(`批次0.2节点注册不完整：期望 ${expectedNodes} 个，实际 ${totalRegistered} 个`);
      return false;
    }

    console.log('批次0.2节点验证通过：所有70个服务器集成节点已正确注册');
    return true;
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 服务器集成节点 (70个)
      // 用户服务节点 (17个)
      'UserAuthentication', 'UserRegistration', 'UserProfile', 'UserPermission', 'UserRole',
      'UserSession', 'UserPreferences', 'UserActivity', 'UserAnalytics', 'UserNotification',
      'UserGroup', 'UserSync', 'UserValidation', 'UserSecurity', 'UserBackup',
      'UserMigration', 'UserAudit',

      // 数据服务节点 (17个)
      'DatabaseConnection', 'DatabaseQuery', 'DatabaseInsert', 'DatabaseUpdate', 'DatabaseDelete',
      'DatabaseTransaction', 'DataValidation', 'DataTransformation', 'DataAggregation', 'DataBackup',
      'DataSync', 'DataAnalytics', 'DataMigration', 'DataCache', 'DataCompression',
      'DataEncryption', 'DataMonitoring',

      // 文件服务节点 (10个)
      'FileUpload', 'FileDownload', 'FileStorage', 'FileCompression', 'FileEncryption',
      'FileVersioning', 'FileMetadata', 'FileSearch', 'FileSync', 'FileAnalytics',

      // 认证授权节点 (10个)
      'JWTToken', 'OAuth2', 'RBAC', 'PermissionCheck', 'SecurityAudit',
      'Encryption', 'Decryption', 'SecurityMonitoring', 'SecurityPolicy', 'SecurityCompliance',

      // 通知服务节点 (10个)
      'EmailNotification', 'PushNotification', 'SMSNotification', 'InAppNotification', 'NotificationTemplate',
      'NotificationSchedule', 'NotificationAnalytics', 'NotificationPreferences', 'NotificationHistory', 'NotificationOptimization',

      // 监控服务节点 (9个)
      'SystemMonitoring', 'PerformanceMonitoring', 'ErrorTracking', 'LogAnalysis', 'AlertSystem',
      'MetricsCollection', 'HealthCheck', 'ResourceMonitoring', 'NetworkMonitoring',

      // 项目管理节点 (10个)
      'CreateProject', 'LoadProject', 'SaveProject', 'ProjectVersion', 'ProjectCollaboration',
      'ProjectPermission', 'ProjectBackup', 'ProjectAnalytics', 'ProjectTemplate', 'ProjectExport',

      // 其他系统集成节点 (108个)
      // 输入系统节点 (25个)
      'MultiTouchNode', 'GestureRecognitionNode', 'VoiceInputNode', 'MotionSensorNode', 'AccelerometerNode',
      'GyroscopeNode', 'CompassNode', 'ProximityNode', 'LightSensorNode', 'PressureSensorNode',
      'VRControllerInputNode', 'VRHeadsetTrackingNode', 'ARTouchInputNode', 'ARGestureInputNode', 'SpatialInputNode',
      'EyeTrackingInputNode', 'HandTrackingInputNode', 'VoiceCommandInputNode', 'GamepadInputNode', 'KeyboardInputNode',
      'MouseInputNode', 'TouchInputNode', 'PenInputNode', 'MIDIInputNode', 'CustomInputNode',

      // 动画扩展节点 (15个)
      'AnimationTimelineNode', 'AnimationBlendTreeNode', 'AnimationStateMachineNode', 'IKSystemNode', 'AnimationRetargetingNode',
      'AnimationCompressionNode', 'AnimationOptimizationNode', 'AnimationBakingNode', 'AnimationExportNode', 'AnimationImportNode',
      'AnimationValidationNode', 'AnimationLayerNode', 'AnimationBlendingNode', 'AnimationCurveNode', 'KeyframeEditorNode',

      // 音频扩展节点 (9个)
      'AudioMixerNode', 'AudioEffectChainNode', 'AudioReverbNode', 'AudioEQNode', 'AudioCompressorNode',
      'AudioDelayNode', 'AudioChorusNode', 'AudioDistortionNode', 'AudioOptimizationNode',

      // 物理扩展节点 (11个)
      'SoftBodyPhysicsNode', 'FluidSimulationNode', 'ClothSimulationNode', 'RopeSimulationNode', 'DestructionNode',
      'PhysicsConstraintNode', 'PhysicsJointNode', 'PhysicsMotorNode', 'PhysicsOptimizationNode', 'PhysicsLODNode',
      'PhysicsPerformanceMonitorNode',

      // 粒子系统节点 (8个)
      'ParticleSystemEditorNode', 'ParticleEmitterEditorNode', 'ParticlePreviewNode', 'ParticleLibraryNode',
      'ParticleExportNode', 'ParticleImportNode', 'ParticleForceEditorNode', 'ParticleCollisionEditorNode',

      // 地形编辑节点 (10个)
      'TerrainSculptingNode', 'TerrainPaintingNode', 'TerrainTextureNode', 'TerrainVegetationNode', 'TerrainWaterNode',
      'TerrainOptimizationNode', 'TerrainExportNode', 'TerrainImportNode', 'TerrainHeightmapNode', 'TerrainErosionNode',

      // 动作捕捉节点 (6个)
      'CameraInputNode', 'MotionCaptureInitNode', 'SkeletonTrackingNode', 'FaceTrackingNode', 'HandTrackingNode', 'BodyTrackingNode',

      // 其他模块节点 (24个)
      'UserInteractionNode', 'TouchInteractionNode', 'MouseInteractionNode', 'KeyboardInteractionNode',
      'InteractionEventNode', 'InteractionStateNode', 'InteractionFeedbackNode', 'InteractionHistoryNode',
      'AvatarCreationNode', 'AvatarCustomizationNode', 'FacialExpressionNode', 'AvatarAnimationNode',
      'AvatarPhysicsNode', 'AvatarClothingNode', 'AvatarSkinNode', 'AvatarEmotionNode',
      'PerformanceProfilerNode', 'DebugVisualizationNode', 'MemoryManagementNode', 'ResourceMonitorNode',
      'ErrorHandlingNode', 'LoggingSystemNode', 'ConfigurationNode', 'UtilityToolsNode'
    ];
  }

  /**
   * 检查节点是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 重置注册状态（用于测试）
   */
  public resetRegistration(): void {
    this.registered = false;
    this.registeredNodes.clear();
  }
}

/**
 * 导出批次0.2服务器集成节点注册函数
 */
export function registerBatch02Nodes(): void {
  const registry = Batch02NodesRegistry.getInstance();
  registry.registerAllNodes();
}

/**
 * 导出批次0.2服务器集成节点验证函数
 */
export function validateBatch02Nodes(): boolean {
  const registry = Batch02NodesRegistry.getInstance();
  return registry.validateBatch02Nodes();
}

// 导出单例实例
export const batch02NodesRegistry = Batch02NodesRegistry.getInstance();
